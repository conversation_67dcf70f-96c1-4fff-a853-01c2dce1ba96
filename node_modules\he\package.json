{"_from": "he@^1.2.0", "_id": "he@1.2.0", "_inBundle": false, "_integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "_location": "/he", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "he@^1.2.0", "name": "he", "escapedName": "he", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/vue-template-compiler"], "_resolved": "https://registry.npmmirror.com/he/-/he-1.2.0.tgz", "_shasum": "84ae65fa7eafb165fddb61566ae14baf05664f0f", "_spec": "he@^1.2.0", "_where": "D:\\project\\testP10cert\\node_modules\\vue-template-compiler", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bin": {"he": "bin/he"}, "bugs": {"url": "https://github.com/mathiasbynens/he/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A robust HTML entities encoder/decoder with full Unicode support.", "devDependencies": {"codecov.io": "^0.1.6", "grunt": "^0.4.5", "grunt-cli": "^1.3.1", "grunt-shell": "^1.1.1", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "jsesc": "^1.0.0", "lodash": "^4.8.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "regexgen": "^1.3.0", "requirejs": "^2.1.22", "sort-object": "^3.0.2"}, "directories": {"bin": "bin", "man": "man", "test": "tests"}, "files": ["LICENSE-MIT.txt", "he.js", "bin/", "man/"], "homepage": "https://mths.be/he", "keywords": ["string", "entities", "entity", "html", "encode", "decode", "unicode"], "license": "MIT", "main": "he.js", "man": ["D:\\project\\testP10cert\\node_modules\\he\\man\\he.1"], "name": "he", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/he.git"}, "scripts": {"build": "grunt build", "test": "node tests/tests.js"}, "version": "1.2.0"}