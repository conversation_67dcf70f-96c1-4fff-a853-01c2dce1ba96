{"_from": "sm-crypto", "_id": "sm-crypto@0.3.13", "_inBundle": false, "_integrity": "sha512-ztNF+pZq6viCPMA1A6KKu3bgpkmYti5avykRHbcFIdSipFdkVmfUw2CnpM2kBJyppIalqvczLNM3wR8OQ0pT5w==", "_location": "/sm-crypto", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "sm-crypto", "name": "sm-crypto", "escapedName": "sm-crypto", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/sm-crypto/-/sm-crypto-0.3.13.tgz", "_shasum": "9615d67f9f2280970c353122e5901ae87d64899a", "_spec": "sm-crypto", "_where": "D:\\project\\testP10cert", "author": {"name": "june_01"}, "bugs": {"url": "https://github.com/JuneAndGreen/sm-crypto/issues"}, "bundleDependencies": false, "dependencies": {"jsbn": "^1.1.0"}, "deprecated": false, "description": "sm-crypto", "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-preset-es2015": "^6.24.1", "eslint": "^5.3.0", "eslint-config-airbnb-base": "13.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "jest": "^22.1.4", "webpack": "^3.10.0"}, "homepage": "https://github.com/JuneAndGreen/sm-crypto#readme", "jest": {"testEnvironment": "jsdom", "testURL": "https://jest.test"}, "keywords": ["sm", "js", "crypto"], "license": "MIT", "main": "src/index.js", "name": "sm-crypto", "repository": {"type": "git", "url": "git+https://github.com/JuneAndGreen/sm-crypto.git"}, "scripts": {"build": "npm run lint && webpack", "lint": "eslint \"src/**/*.js\" --fix", "prepublish": "npm run build", "test": "jest ./test/*"}, "version": "0.3.13"}