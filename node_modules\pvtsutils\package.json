{"_from": "pvtsutils@^1.3.2", "_id": "pvtsutils@1.3.5", "_inBundle": false, "_integrity": "sha512-ARvb14YB9Nm2Xi6nBq1ZX6dAM0FsJnuk+31aUp4TrcZEdKUlSqOqsxJHUPJDNE3qiIp+iUPEIeR6Je/tgV7zsA==", "_location": "/pvtsutils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pvtsutils@^1.3.2", "name": "pvtsutils", "escapedName": "pvtsutils", "rawSpec": "^1.3.2", "saveSpec": null, "fetchSpec": "^1.3.2"}, "_requiredBy": ["/asn1js"], "_resolved": "https://registry.npmmirror.com/pvtsutils/-/pvtsutils-1.3.5.tgz", "_shasum": "b8705b437b7b134cd7fd858f025a23456f1ce910", "_spec": "pvtsutils@^1.3.2", "_where": "D:\\project\\testP10cert\\node_modules\\asn1js", "author": {"name": "PeculiarVentures"}, "browser": "build/index.js", "bugs": {"url": "https://github.com/PeculiarVentures/pvtsutils/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"tslib": "^2.6.1"}, "deprecated": false, "description": "pvtsutils is a set of common utility functions used in various Peculiar Ventures TypeScript based projects.", "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^20.4.8", "coveralls": "^3.1.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "rimraf": "^5.0.1", "rollup": "^3.27.2", "rollup-plugin-dts": "^5.3.1", "rollup-plugin-typescript2": "^0.35.0", "ts-node": "^10.9.1", "tslint": "^6.1.3", "typescript": "^5.1.6"}, "files": ["build/**/*.{ts,js}", "README.md", "LICENSE"], "homepage": "https://github.com/PeculiarVentures/pvtsutils#readme", "keywords": ["typescript", "helper", "util", "convert", "hex", "utf8", "utf16", "base64", "base64url", "binary", "assign"], "license": "MIT", "main": "build/index.js", "module": "build/index.es.js", "name": "pvtsutils", "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/pvtsutils.git"}, "resolutions": {"json5": "^2.2.2", "semver": "^6.3.1", "tough-cookie": "^4.1.3"}, "scripts": {"build": "rollup -c", "clear": "rimraf build/*", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "tslint -p .", "lint:fix": "tslint --fix -p .", "postpub": "git push && git push --tags origin master", "postpub:next": "git push", "prepub": "npm run lint && npm run rebuild", "prepub:next": "npm run lint && npm run rebuild", "pub": "npm version patch && npm publish", "pub:next": "npm version prerelease --preid=next && npm publish --tag next", "rebuild": "npm run clear && npm run build", "test": "mocha"}, "types": "build/index.d.ts", "version": "1.3.5"}