const asn1js = require("asn1js");
const { Buffer } = require("buffer");
const sm2 = require("sm-crypto").sm2;

// 生成 SM2 密钥对
const { privateKey, publicKey } = sm2.generateKeyPairHex();

console.log("privateKey: ", privateKey); // 私钥
console.log("publicKey: ", publicKey); // 公钥

// 定义证书请求的主体信息
const subject = {
  CN: "test", // 通用名
};

function formatPEM(pem) {
  return pem.match(/.{1,76}/g).join("\n");
}

function intToSignedBytes(num) {
  const bytes = [];
  for (let i = 0; i < 4; i++) {
      bytes.push((num >> (8 * (3 - i))) & 0xFF);
  }
  return bytes.map(byte => byte >= 128 ? byte - 256 : byte); // 转换为有符号
}

function bufferToSignedHex(buffer) {
  const int8Array = new Int8Array(buffer);
  return Array.from(int8Array)
    .map((byte) => (byte < 0 ? (256 + byte).toString(16) : byte.toString(16)).padStart(2, '0'))
    .join('');
}


// 构建 PKCS#10 证书请求 ASN.1 结构
function createP10Request(subject, publicKeyHex, privateKeyHex) {
  const sm2PublicKeyOID = "1.2.156.10197.1.301"; // SM2 公钥算法 OID
  const sm2SignAlgOID = "1.2.156.10197.1.501"; // SM2 + SM3 签名算法 OID

  // CertificationRequestInfo ASN.1 构建
  const certReqInfo = new asn1js.Sequence({
    value: [
      new asn1js.Integer({ value: 0 }), // 版本号 v1
      new asn1js.Sequence({
        // subject
        value: [
          new asn1js.Set({
            value: [
              new asn1js.Sequence({
                value: [
                  new asn1js.ObjectIdentifier({ value: "2.5.4.3" }), // CN
                  new asn1js.Utf8String({ value: subject.CN }),
                ],
              }),
            ],
          }),
        ],
      }),
      new asn1js.Sequence({
        // subjectPKInfo
        value: [
          new asn1js.Sequence({
            // AlgorithmIdentifier
            value: [
              new asn1js.ObjectIdentifier({ value: "1.2.840.10045.2.1" }),
              new asn1js.ObjectIdentifier({ value: sm2PublicKeyOID }),
            ],
          }),
          new asn1js.BitString({
            valueHex: Buffer.from(publicKeyHex, "hex"),
            unusedBits: 0, // 确保填充位设置正确
          }),
        ],
      }),
      new asn1js.Constructed({
        idBlock: {
          tagClass: 3, // Context-specific
          tagNumber: 0, // Tag number [0]
          isConstructed: false,
        },
        value: [], // UTF8String 对象作为标签的值
      }),
    ],
  });

  // 对 certReqInfo 进行 DER 编码
  const certReqInfoHex = certReqInfo.toBER(false).toString("hex");

  // SignatureAlgorithm ASN.1 构建
  const sigAlg = new asn1js.Sequence({
    value: [
      new asn1js.ObjectIdentifier({ value: sm2SignAlgOID }), // SM2 签名算法 OID
      new asn1js.Null(),
    ],
  });

  // 使用 SM2 签名
  const csrInfoHex = Buffer.from(certReqInfo.toBER());
  // const csrInfoHex = certReqInfo.toBER();
  console.log("CSR 信息：", certReqInfoHex, "长度：", certReqInfoHex.length);


  let c = csrInfoHex.map((item,index) => {
    // let l = intToSignedBytes(item)[3];
    let l = item.toString(16);
    console.log(index+"==:", l,l.toString(16),l.toString(16).length);
    console.log(l.length === 1 ? '0' + (l+"") : (l+""));
    return l.length === 1 ? '0' + l : l
  }).join('');

  let datas ="";
  csrInfoHex.forEach((item,index) => {
    let l = item.toString(16);
    datas+= l.length === 1 ? '0' + l : l
  });

  // let c = bufferToSignedHex(csrInfoHex);
console.log("字符串拼接:"+datas);
  console.log("111:"+c);

  const csrInfoHex111 = Buffer.from(datas);

  const signature = sm2.doSignature(datas, privateKeyHex, {
    hash: true,
    der: true,
  });
  console.log("签名：", signature, "长度：", signature.length);

  let verifyResult = sm2.doVerifySignature(datas, signature, publicKeyHex,{
    hash: true,
    der: true,
  }) // 验
  console.log("验签结果FFFFFFFFF：", verifyResult);

  // 转换为16进制格式
  const signatureHex = Buffer.from(signature, "hex");

  // CertificationRequest ASN.1 构建
  const csr = new asn1js.Sequence({
    value: [
      certReqInfo,
      sigAlg,
      new asn1js.BitString({
        valueHex: signatureHex
      }),
    ],
  });
  console.log("CSR 长度：", csr.toBER(false).byteLength);
  // 生成 PEM 格式的 CSR
  const csrPem = Buffer.from(csr.toBER(false)).toString("base64");
  return `-----BEGIN CERTIFICATE REQUEST-----\n${csrPem}\n-----END CERTIFICATE REQUEST-----`;
}

var pub =
  "04b4140946abe45c3ddc5ac836b1865d3979b6973338e169c6e76daad0fbfa470b5ac2c0e2048432328b00f9dd00bdf4a3212cc4bbda86458b80d7e69c79be0ff3";
var pri = "72bbcf10a6d0ca94a3ab82f0caf4dfc12827c29bffe6c6148c853386e6a30884";
  const csrInfoHex = Buffer.from("123456");

let sigValueHex = sm2.doSignature(csrInfoHex, pri,{
  hash: true,
  der: true,
}) // 签
console.log("签名结果：", sigValueHex);
let verifyResult = sm2.doVerifySignature(csrInfoHex, sigValueHex, pub,{
  hash: true,
  der: true,
}) // 验

console.log("验签结果：", verifyResult);

const p10Request = createP10Request(subject, pub, pri);
console.log("P10 请求数据（PEM 格式）：\n", p10Request);
