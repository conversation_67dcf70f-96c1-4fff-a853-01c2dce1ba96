const jsrsasign = require("jsrsasign");
// const sm2 = require("sm-crypto").sm2;

// RSA
const keypair = jsrsasign.KEYUTIL.generateKeypair("RSA", 2048);

const privateKey = keypair.prvKeyObj;
const publicKey = keypair.pubKeyObj;

var params = {
  subject: { str: "/C=US/O=Test/CN=CN" },
  sbjpubkey: publicKey,
  sigalg: "SHA256withRSA",
  sbjprvkey: privateKey,
};

var csr = new jsrsasign.asn1.csr.CertificationRequest(params);

// ECC
// const { privateKeyECC, publicKeyECC } = sm2.generateKeyPairHex();

// var params = {
//   subject: { str: "/C=US/O=Test/CN=CN" },
//   sbjpubkey: publicKeyECC,
//   sigalg: "SM3withSM2",
//   sbjprvkey: privateKeyECC,
// };

// var csr = new jsrsasign.asn1.csr.CertificationRequest(params);

console.log(csr.getPEM());
