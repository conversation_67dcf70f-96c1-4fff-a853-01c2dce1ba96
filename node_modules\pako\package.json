{"_from": "pako", "_id": "pako@2.1.0", "_inBundle": false, "_integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "_location": "/pako", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "pako", "name": "pako", "escapedName": "pako", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/pako/-/pako-2.1.0.tgz", "_shasum": "266cc37f98c7d883545d11335c00fbd4062c9a86", "_spec": "pako", "_where": "D:\\project\\testP10cert", "bugs": {"url": "https://github.com/nodeca/pako/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/andr83"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/puzrin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dignifiedquire"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kirill89"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}], "dependencies": {}, "deprecated": false, "description": "zlib port to javascript - fast, modularized, with browser support", "devDependencies": {"@babel/preset-env": "^7.12.1", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-node-resolve": "^10.0.0", "eslint": "^7.13.0", "gh-pages": "^3.1.0", "mocha": "^8.2.1", "multiparty": "^4.1.3", "ndoc": "^6.0.0", "nyc": "^15.1.0", "rollup": "^2.33.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4"}, "exports": {".": {"import": "./dist/pako.esm.mjs", "require": "./index.js"}, "./package.json": "./package.json", "./dist/*": "./dist/*", "./lib/*": "./lib/*", "./lib/zlib/*": "./lib/zlib/*", "./lib/utils/*": "./lib/utils/*"}, "files": ["index.js", "dist/", "lib/"], "homepage": "https://github.com/nodeca/pako#readme", "keywords": ["zlib", "deflate", "inflate", "gzip"], "license": "(MIT AND Zlib)", "module": "./dist/pako.esm.mjs", "name": "pako", "repository": {"type": "git", "url": "git+https://github.com/nodeca/pako.git"}, "scripts": {"build": "rollup -c", "build_fixtures": "node support/build_fixtures.js", "coverage": "npm run lint && nyc mocha && nyc report --reporter html", "doc": "node support/build_doc.js", "gh-doc": "npm run doc && gh-pages -d doc -f", "lint": "eslint .", "prepublishOnly": "npm run gh-doc", "test": "npm run lint && mocha"}, "version": "2.1.0"}