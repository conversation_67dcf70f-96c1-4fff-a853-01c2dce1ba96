{"_from": "pcap-parser", "_id": "pcap-parser@0.2.1", "_inBundle": false, "_integrity": "sha512-+1t1GiMpEHI+MFub/mpCmfpyU4oVOyn4h71Zp5GqC/2uv0yteM6MghazKBQMkNXgmmsCPT1JUMfqsF03cYjnyw==", "_location": "/pcap-parser", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "pcap-parser", "name": "pcap-parser", "escapedName": "pcap-parser", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/pcap-parser/-/pcap-parser-0.2.1.tgz", "_shasum": "0a797ae810c28ff08ce9108bc509d323bfe6aa6f", "_spec": "pcap-parser", "_where": "D:\\project\\testP10cert", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nearinfinity/node-pcap-parser/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Packet capture (PCAP) parser for node", "devDependencies": {"vows": "~0.5.13"}, "engines": {"node": ">=0.6.0"}, "homepage": "https://github.com/nearinfinity/node-pcap-parser#readme", "keywords": ["pcap", "parser"], "license": "MIT", "main": "./lib/pcap-parser.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "name": "pcap-parser", "repository": {"type": "git", "url": "git+https://github.com/nearinfinity/node-pcap-parser.git"}, "scripts": {"test": "vows"}, "version": "0.2.1"}