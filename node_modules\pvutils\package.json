{"_from": "pvutils@^1.1.3", "_id": "pvutils@1.1.3", "_inBundle": false, "_integrity": "sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==", "_location": "/pvutils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pvutils@^1.1.3", "name": "pvutils", "escapedName": "pvutils", "rawSpec": "^1.1.3", "saveSpec": null, "fetchSpec": "^1.1.3"}, "_requiredBy": ["/asn1js"], "_resolved": "https://registry.npmmirror.com/pvutils/-/pvutils-1.1.3.tgz", "_shasum": "f35fc1d27e7cd3dfbd39c0826d173e806a03f5a3", "_spec": "pvutils@^1.1.3", "_where": "D:\\project\\testP10cert\\node_modules\\asn1js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/PeculiarVentures/pvutils/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Common utilities for products from Peculiar Ventures", "devDependencies": {"@types/mocha": "^9.1.0", "@types/node": "^17.0.19", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "eslint": "^8.9.0", "eslint-plugin-import": "^2.25.4", "mocha": "^9.2.1", "nyc": "^15.1.0", "rollup": "2.68.0", "rollup-plugin-dts": "^4.1.0", "rollup-plugin-typescript2": "^0.31.2", "ts-node": "^10.5.0", "typescript": "^4.5.5"}, "engines": {"node": ">=6.0.0"}, "files": ["build", "README.md", "LICENSE"], "homepage": "https://github.com/PeculiarVentures/pvutils#readme", "license": "MIT", "main": "./build/utils.js", "module": "./build/utils.es.js", "name": "pvutils", "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/pvutils.git"}, "scripts": {"build": "rollup -c", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "prepare": "npm run build", "test": "mocha"}, "types": "./build/index.d.ts", "version": "1.1.3"}