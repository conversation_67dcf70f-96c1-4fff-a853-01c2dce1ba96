/* eslint-disable no-bitwise, no-mixed-operators, no-use-before-define, max-len */
const {BigInteger, SecureRandom} = require('jsbn')
const {ECCurveFp} = require('./ec')
const _ = require('./utils')
const sm3 = require('./sm3')

const rng = new SecureRandom()
const {curve, G, n} = generateEcparam()


/**
 * 获取公共椭圆曲线
 */
function getGlobalCurve() {
  return curve
}




/**
 * 生成ecparam
 */
function generateEcparam() {
  // 椭圆曲线
  const p = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF', 16)
  const a = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC', 16)
  const b = new BigInteger('28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93', 16)
  const curve = new ECCurveFp(p, a, b)
  // 基点
  const gxHex = '32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7'
  const gyHex = 'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0'
  const G = curve.decodePointHex('04' + gxHex + gyHex)

  const n = new BigInteger('FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123', 16)

  return {curve, G, n}
}


// eslint-disable-next-line no-unused-vars
function createDp() {
  const d = createD()
  const p = createECPoint(d)
  return {d, p}
}


function test(){
  //93df2c55d6c039437ec0797b65ce7eea4b9249376ee77003c90c05ead53258db
  new BigInteger()
  const p = createECPoint("93df2c55d6c039437ec0797b65ce7eea4b9249376ee77003c90c05ead53258db")
  console.log(p)
}
test()

/**
 * B1 生成随机数d2ϵ[1,n-1]
 */
// eslint-disable-next-line no-unused-vars
function createD() {
  const random = new BigInteger(n.bitLength(), rng)
  const d = random.mod(n.subtract(BigInteger.ONE)).add(BigInteger.ONE) // 随机数return d
  return d
}

/*
 * B2 生成P2=[d2]G
 */
// eslint-disable-next-line no-unused-vars
function createECPoint(d) {
  return G.multiply(d)
}

// 计算协同公钥 返回（d1,p1,PA）
// eslint-disable-next-line no-unused-vars
function generatePA(p2) {
  const d1 = createD() // A3 d1ϵ[1,n-1]
  // A4 P1=[d1]G
  const p1 = createECPoint(d1).normalize() // A4 P1=[d1]G
  // A5 协同之后公钥
  const PA = p2.multiply(d1).subtract(G).normalize() // A5 [d1]P2-G
  return {d1, p1, PA}
}

// eslint-disable-next-line no-unused-vars
function nomarlSignViry(msg, publicKey, d1) {
  // A1
  const hash = getHash(msg, publicKey)
  const e = new BigInteger(hexToArray(hash), 16)

  // eslint-disable-next-line no-unused-vars
  let k1; let r; let s2; let s; let s1Inverse
  do {
    do {
      // A2 产生随机数k1∈[1,n-1]；
      k1 = nexK()
      const p2 = getP2()
      // A3：计算椭圆曲线点Q1=[k1 ]P2；
      const Q1 = p2.multiply(k1).normalize()
      // A4 将e,Q1 发送给协作方
      const {R, S} = calRs(Q1, e)
      r = R
      s2 = S
    } while (k1.add(s2).equals(n))
    // A5  (d1的逆无⋅(k1+s2)-r) mod n
    // eslint-disable-next-line prefer-const
    s1Inverse = d1.modInverse(n)
    s = s1Inverse.multiply(k1.add(s2)).subtract(r).mod(n)
  } while (s.equals(BigInteger.ZERO))

  
}

// 这里的p2应该是从服务器获取
function getP2() {

}

// 服务器获取R,S
function calRs(q1, e) {
  // B2 产生随机数k2 ϵ[1,n-1]
  let k2
  let b3Point
  let r
  const d2 = getD2()
  do {
    // B2 产生随机数k1∈[1,n-1]；
    k2 = nexK()
    // B3：计算椭圆曲线点(x1,y1)=[k2]G+Q1
    b3Point = G.multiply(k2).add(q1).normalize()
    // B4: 计算r=(e+x1)mod n
    r = e.add(b3Point.getX().toBigInteger()).mod(n)
  } while (r.equals(BigInteger.ZERO) || r.add(k2).equals(n))
  // 计算s2=(d2-1.(r+k2))mod n
  const d2Inverse = d2.modInverse(n)
  const rAddK2 = r.add(k2)
  const s2 = d2Inverse.multiply(rAddK2).mod(n)
  return {r, s2}
}


function getD2() {

}

// eslint-disable-next-line no-unused-vars
function nexK() {
  let k
  do {
    k = createD()
  } while (k.equals(BigInteger.ZERO) || k.compareTo(n))
  return k
}


/**
 * sm3杂凑算法
 */
function getHash(hashHex, publicKey, userId = '1234567812345678') {
  // z = hash(entl || userId || a || b || gx || gy || px || py)
  userId = _.utf8ToHex(userId)
  const a = _.leftPad(G.curve.a.toBigInteger().toRadix(16), 64)
  const b = _.leftPad(G.curve.b.toBigInteger().toRadix(16), 64)
  const gx = _.leftPad(G.getX().toBigInteger().toRadix(16), 64)
  const gy = _.leftPad(G.getY().toBigInteger().toRadix(16), 64)
  let px
  let py
  if (publicKey.length === 128) {
    px = publicKey.substr(0, 64)
    py = publicKey.substr(64, 64)
  } else {
    const point = G.curve.decodePointHex(publicKey)
    px = _.leftPad(point.getX().toBigInteger().toRadix(16), 64)
    py = _.leftPad(point.getY().toBigInteger().toRadix(16), 64)
  }
  const data = _.hexToArray(userId + a + b + gx + gy + px + py)

  const entl = userId.length * 4
  data.unshift(entl & 0x00ff)
  data.unshift(entl >> 8 & 0x00ff)

  const z = sm3(data)

  // e = hash(z || msg)
  return _.arrayToHex(sm3(z.concat(_.hexToArray(hashHex))))
}

/**
 *
 * 生成压缩公钥
 */
function compressPublicKeyHex(s) {
  if (s.length !== 130) throw new Error('Invalid public key to compress')

  const len = (s.length - 2) / 2
  const xHex = s.substr(2, len)
  const y = new BigInteger(s.substr(len + 2, len), 16)

  let prefix = '03'
  if (y.mod(new BigInteger('2')).equals(BigInteger.ZERO)) prefix = '02'

  return prefix + xHex
}


// 验证是否为椭圆曲线上的点
// eslint-disable-next-line no-unused-vars
function isValid(x, y) {
  const pointToVerify = curve.decodePointHex(x + y)
  return pointToVerify.isValid()
}

/**
 * utf8串转16进制串
 */
function utf8ToHex(input) {
  input = unescape(encodeURIComponent(input))

  const length = input.length

  // 转换到字数组
  const words = []
  for (let i = 0; i < length; i++) {
    words[i >>> 2] |= (input.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8)
  }

  // 转换到16进制
  const hexChars = []
  for (let i = 0; i < length; i++) {
    const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
    hexChars.push((bite >>> 4).toString(16))
    hexChars.push((bite & 0x0f).toString(16))
  }

  return hexChars.join('')
}

/**
 * 补全16进制字符串
 */
function leftPad(input, num) {
  if (input.length >= num) return input

  return (new Array(num - input.length + 1)).join('0') + input
}

/**
 * 转成16进制串
 */
function arrayToHex(arr) {
  return arr.map(item => {
    item = item.toString(16)
    return item.length === 1 ? '0' + item : item
  }).join('')
}

/**
 * 转成utf8串
 */
function arrayToUtf8(arr) {
  const words = []
  let j = 0
  for (let i = 0; i < arr.length * 2; i += 2) {
    words[i >>> 3] |= parseInt(arr[j], 10) << (24 - (i % 8) * 4)
    j++
  }

  try {
    const latin1Chars = []

    for (let i = 0; i < arr.length; i++) {
      const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
      latin1Chars.push(String.fromCharCode(bite))
    }

    return decodeURIComponent(escape(latin1Chars.join('')))
  } catch (e) {
    throw new Error('Malformed UTF-8 data')
  }
}

/**
 * 转成字节数组
 */
function hexToArray(hexStr) {
  const words = []
  let hexStrLength = hexStr.length

  if (hexStrLength % 2 !== 0) {
    hexStr = leftPad(hexStr, hexStrLength + 1)
  }

  hexStrLength = hexStr.length

  for (let i = 0; i < hexStrLength; i += 2) {
    words.push(parseInt(hexStr.substr(i, 2), 16))
  }
  return words
}

/**
 * 验证公钥是否为椭圆曲线上的点
 */
function verifyPublicKey(publicKey) {
  const point = curve.decodePointHex(publicKey)
  if (!point) return false

  const x = point.getX()
  const y = point.getY()

  // 验证 y^2 是否等于 x^3 + ax + b
  return y.square().equals(x.multiply(x.square()).add(x.multiply(curve.a)).add(curve.b))
}

/**
 * 验证公钥是否等价，等价返回true
 */
function comparePublicKeyHex(publicKey1, publicKey2) {
  const point1 = curve.decodePointHex(publicKey1)
  if (!point1) return false

  const point2 = curve.decodePointHex(publicKey2)
  if (!point2) return false

  return point1.equals(point2)
}

module.exports = {
  getGlobalCurve,
  generateEcparam,
  generateKeyPairHex,
  compressPublicKeyHex,
  utf8ToHex,
  leftPad,
  arrayToHex,
  arrayToUtf8,
  hexToArray,
  verifyPublicKey,
  comparePublicKeyHex,
}
