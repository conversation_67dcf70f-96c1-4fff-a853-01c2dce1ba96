{"_from": "asn1js", "_id": "asn1js@3.0.5", "_inBundle": false, "_integrity": "sha512-FVnvrKJwpt9LP2lAMl8qZswRNm3T4q9CON+bxldk2iwk3FFpuwhx2FfinyitizWHsVYyaY+y5JzDR0rCMV5yTQ==", "_location": "/asn1js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "asn1js", "name": "asn1js", "escapedName": "asn1js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/asn1js/-/asn1js-3.0.5.tgz", "_shasum": "5ea36820443dbefb51cc7f88a2ebb5b462114f38", "_spec": "asn1js", "_where": "D:\\project\\testP10cert", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/PeculiarVentures/asn1.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"pvtsutils": "^1.3.2", "pvutils": "^1.1.3", "tslib": "^2.4.0"}, "deprecated": false, "description": "asn1js is a pure JavaScript library implementing this standard. ASN.1 is the basis of all X.509 related data structures and numerous other protocols used on the web", "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "^17.0.32", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "asn1-test-suite": "^1.0.2", "eslint": "^8.15.0", "eslint-plugin-deprecation": "^1.3.2", "mocha": "^10.0.0", "nyc": "^15.1.0", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-typescript2": "^0.31.2", "ts-node": "^10.7.0", "typescript": "^4.6.4"}, "engines": {"node": ">=12.0.0"}, "files": ["build", "LICENSE", "README.md"], "homepage": "https://github.com/PeculiarVentures/asn1.js#readme", "keywords": ["asn1", "parser", "asn.1", "ber", "der", "sequence", "set", "bitstring", "octetstring", "utctime", "utf8string", "bmpstring", "universalstring", "generalizedtime"], "license": "BSD-3-<PERSON><PERSON>", "main": "build/index.js", "module": "build/index.es.js", "name": "asn1js", "repository": {"type": "git", "url": "git://github.com/PeculiarVentures/asn1.js.git"}, "scripts": {"build": "rollup -c", "coverage": "nyc npm test", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "prepublishOnly": "npm run build", "test": "mocha"}, "types": "build/index.d.ts", "version": "3.0.5", "warnings": [{"code": "ENOTSUP", "required": {"node": ">=12.0.0"}, "pkgid": "asn1js@3.0.5"}, {"code": "ENOTSUP", "required": {"node": ">=12.0.0"}, "pkgid": "asn1js@3.0.5"}]}