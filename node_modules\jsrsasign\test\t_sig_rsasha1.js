var assert = require('assert');
var rs = require('../lib/jsrsasign.js');

// z1.pkcs1.pem
var _Z1PKCS1PEM = (function() {/*
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/}).toString().match(/\/\*([^]*)\*\//)[1];

var _hSigAAA = "6f7df91d8f973a0619d525c319337741130b77b21f9667dc7d1d74853b644cbe5e6b0e84aacc2faee883d43affb811fc653b67c38203d4f206d1b838c4714b6b2cf17cd621303c21bac96090df3883e58784a0576e501c10cdefb12b6bf887e548f6b07b09ae80d8416151d7dab7066d645e2eee57ac5f7af2a70ee0724c8e47";

describe("KJUR.crypto.Signature.sign SHA1withRSA", function() {
  describe("z1.pkcs1.pem", function() {
    var sig = new rs.KJUR.crypto.Signature({alg: "SHA1withRSA"});
    sig.init(_Z1PKCS1PEM);
    sig.updateString("aaa");
    it('load and sign properly', function() {
      assert.equal(_hSigAAA, sig.sign());
    });
  });
});
