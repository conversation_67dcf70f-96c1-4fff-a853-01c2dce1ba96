{"_from": "jsbn@^1.1.0", "_id": "jsbn@1.1.0", "_inBundle": false, "_integrity": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==", "_location": "/jsbn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "jsbn@^1.1.0", "name": "jsbn", "escapedName": "jsbn", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/sm-crypto"], "_resolved": "https://registry.npmmirror.com/jsbn/-/jsbn-1.1.0.tgz", "_shasum": "b01307cb29b618a1ed26ec79e911f803c4da0040", "_spec": "jsbn@^1.1.0", "_where": "D:\\project\\testP10cert\\node_modules\\sm-crypto", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/andyperlitch/jsbn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The jsbn library is a fast, portable implementation of large-number math in pure JavaScript, enabling public-key crypto and other applications on desktop and mobile browsers.", "homepage": "https://github.com/andyperlitch/jsbn#readme", "keywords": ["biginteger", "bignumber", "big", "integer"], "license": "MIT", "main": "index.js", "name": "jsbn", "repository": {"type": "git", "url": "git+https://github.com/andyperlitch/jsbn.git"}, "scripts": {"test": "mocha test.js"}, "version": "1.1.0"}