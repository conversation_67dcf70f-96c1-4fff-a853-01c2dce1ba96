var pcapp = require("pcap-parser");
const fs = require('fs');

var parser = pcapp.parse("./b.pcap");
let num = 1;
const tcpStreams = {}; // 存储 TCP 流

parser.on("packet", function (packet) {
  const rawData = packet.data;
  if (packet.data.length >= 14) {
    var etherType = packet.data.slice(12, 14).readUInt16BE(0);
    console.log("etherType: "+etherType);
    if (etherType === 0x0800) { //IPv4
      if(packet.data.slice(23, 24).readUInt8(0) === 6) { //TCP
        // tcpStreams用于调研整合分片数据筛选数据
        const ipHeaderLength = (rawData[14] & 0x0F) * 4; // IP头部长度
        const tcpHeaderLength = ((rawData[46] & 0xF0) >> 4) * 4; // TCP头部长度
        const payloadOffset = 14 + ipHeaderLength + tcpHeaderLength;
        const payload = rawData.slice(payloadOffset); // 提取负载数据
        // 提取五元组信息
        const srcIP = rawData.slice(26, 30).join('.');
        const dstIP = rawData.slice(30, 34).join('.');
        const srcPort = rawData.readUInt16BE(34);
        const dstPort = rawData.readUInt16BE(36);
        const sequenceNumber = rawData.readUInt32BE(38);
        const streamKey = `${srcIP}:${srcPort}->${dstIP}:${dstPort}`;
        // 初始化 TCP 流
        if (!tcpStreams[streamKey]) {
            tcpStreams[streamKey] = { segments: [], buffer: '' };
        }
        // 保存数据包负载及序列号
        tcpStreams[streamKey].segments.push({ sequenceNumber, payload });

        console.log("TCP数据包("+num+")");
        console.log("src地址："+packet.data.slice(26, 27).readUInt8(0)+"."+packet.data.slice(27, 28).readUInt8(0)+"."+packet.data.slice(28, 29).readUInt8(0)+"."+packet.data.slice(29, 30).readUInt8(0));
        console.log("dst地址："+packet.data.slice(30, 31).readUInt8(0)+"."+packet.data.slice(31, 32).readUInt8(0)+"."+packet.data.slice(32, 33).readUInt8(0)+"."+packet.data.slice(33, 34).readUInt8(0));
        console.log("src端口："+packet.data.slice(34, 36).readUInt16BE(0));
        console.log("dst端口："+packet.data.slice(36, 38).readUInt16BE(0));
        console.log("序列号："+packet.data.slice(38, 42).readUInt32BE(0));
        console.log("确认号: "+packet.data.slice(42, 46).readUInt32BE(0));
        console.log("头部长度: "+packet.data.slice(46, 47).readUInt8(0));
        console.log("标志位: "+packet.data.slice(47, 48).readUInt8(0));
        console.log("窗口大小: "+packet.data.slice(48, 50).readUInt16BE(0));
        console.log("校验和: "+packet.data.slice(50, 52).readUInt16BE(0));
        console.log("紧急指针: "+packet.data.slice(52, 54).readUInt16BE(0));
        if(packet.data.slice(34, 36).readUInt16BE(0) === 3306 || packet.data.slice(36, 38).readUInt16BE(0) === 3306){ //3306端口
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(54, 55).readUInt8(0));
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(55, 56).readUInt8(0));
          console.log("TCP Option - Timestamps：");
          console.log("Kind："+packet.data.slice(56, 57).readUInt8(0));
          console.log("Length："+packet.data.slice(57, 58).readUInt8(0));
          console.log("Timestamp value："+packet.data.slice(58, 62).readUInt32BE(0));
          console.log("Timestamp echo reply："+packet.data.slice(62, 66).readUInt32BE(0));
          if(packet.data.slice(66, 67).length !== 0){
            console.log("MyS0L Protocol: ");
            console.log("Packet Length: "+packet.data.slice(66, 67).readUInt8(0));
            console.log("Packet Number: "+packet.data.slice(69, 70).readUInt8(0));
            if(packet.data.slice(66, 67).readUInt8(0) === 1){
              console.log("Number of fields: "+packet.data.slice(70, 71).readUInt8(0));
              console.log("MySOL Protocol - field packet");
              console.log("Packet Length: "+packet.data.slice(71, 72).readUInt8(0));
              console.log("Packet Number: "+packet.data.slice(74, 75).readUInt8(0));
              console.log("Catalog: "+packet.data.slice(75, 79).toString('utf8'));
              console.log("Name: "+cutBufferBeforeFirstZero(packet.data.slice(82)).toString('utf8'));
            }else{
              console.log("Request Command Query: ");
              console.log("Command:"+packet.data.slice(70, 71).readUInt8(0)); // 03是查询请求 0a是握手请求
              console.log("数据: "+packet.data.slice(71).toString('utf8'));
            }
          }else{
            console.log("数据: "+packet.data.slice(71).toString('utf8'));
          }
          num++;
          console.log("--------------------------------------");
          return;
        }
        if(packet.data.slice(54).toString() != "" && packet.data.slice(54).length === 12) {
          console.log("TCP Option - Maximum segment size：");
          console.log("Kind："+packet.data.slice(54, 55).readUInt8(0));
          console.log("Length："+packet.data.slice(55, 56).readUInt8(0));
          console.log("MSS Value: "+packet.data.slice(56, 58).readUInt16BE(0));
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(58, 59).readUInt8(0));
          console.log("TCP Option - Window scale：");
          console.log("Kind："+packet.data.slice(59, 60).readUInt8(0));
          console.log("Length："+packet.data.slice(60, 61).readUInt8(0));
          console.log("Shift count:"+packet.data.slice(61, 62).readUInt8(0));
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(62, 63).readUInt8(0));
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(63, 64).readUInt8(0));
          console.log("TCP Option - SACK permitted：");
          console.log("Kind："+packet.data.slice(64, 65).readUInt8(0));
          console.log("Length："+packet.data.slice(65, 66).readUInt8(0));
        }else if(packet.data.slice(54).toString() != "" && packet.data.slice(54).length === 20) {
          console.log("TCP Option - Maximum segment size：");
          console.log("Kind："+packet.data.slice(54, 55).readUInt8(0));
          console.log("Length："+packet.data.slice(55, 56).readUInt8(0));
          console.log("MSS Value: "+packet.data.slice(56, 58).readUInt16BE(0));
          console.log("TCP Option - SACK permitted：");
          console.log("Kind："+packet.data.slice(58, 59).readUInt8(0));
          console.log("Length："+packet.data.slice(59, 60).readUInt8(0));
          console.log("TCP Option - Timestamps：");
          console.log("Kind："+packet.data.slice(60, 61).readUInt8(0));
          console.log("Length："+packet.data.slice(61, 62).readUInt8(0));
          console.log("Timestamp value："+packet.data.slice(62, 66).readUInt32BE(0));
          console.log("Timestamp echo reply："+packet.data.slice(66, 70).readUInt32BE(0));
          console.log("TCP Option - No-0peration：");
          console.log("Kind："+packet.data.slice(70, 71).readUInt8(0));
          console.log("TCP Option - Window scale：");
          console.log("Kind："+packet.data.slice(71, 72).readUInt8(0));
          console.log("Length："+packet.data.slice(72, 73).readUInt8(0));
          console.log("Shift count："+packet.data.slice(73, 74).readUInt8(0));
        }else if(packet.data.slice(54).toString() != "" && packet.data.slice(54).length > 6){
          try {
            console.log("TCP Option - No-0peration：");
            console.log("Kind："+packet.data.slice(54, 55).readUInt8(0));
            console.log("TCP Option - No-0peration：");
            console.log("Kind："+packet.data.slice(55, 56).readUInt8(0));
            console.log("TCP Option - Timestamps：");
            console.log("Kind："+packet.data.slice(56, 57).readUInt8(0));
            console.log("Length："+packet.data.slice(57, 58).readUInt8(0));
            console.log("Timestamp value："+packet.data.slice(58, 62).readUInt32BE(0));
            console.log("Timestamp echo reply："+packet.data.slice(62, 66).readUInt32BE(0));
            console.log("数据: "+packet.data.slice(66).toString('utf8'));
          } catch (error) {
            console.log("尾部数据解析异常");
          }
        }else{
          // const ipHeaderLength = (packet.data[14] & 0x0F) * 4; // IP头部长度
          // const tcpHeaderLength = ((packet.data[46] & 0xF0) >> 4) * 4; // TCP头部长度
          // const payloadOffset = 14 + ipHeaderLength + tcpHeaderLength; // 有效载荷偏移
          // console.log(payloadOffset);
          console.log("数据: "+packet.data.slice(54).toString('utf8'));
        }
        num++;
        console.log("--------------------------------------");
      }else{
        num++;
      }
    }else{
      num++;
    }
  }else{
    num++;
  }
});

parser.on('end', () => {
  Object.keys(tcpStreams).forEach((streamKey) => {
      const stream = tcpStreams[streamKey];
      // 按序列号排序
      stream.segments.sort((a, b) => a.sequenceNumber - b.sequenceNumber);
      // 拼接
      stream.buffer = stream.segments.map(segment => segment.payload.toString()).join('');
      // 检查是否包含完整的 HTTP 请求
      const requests = stream.buffer.split('\r\n\r\n'); // HTTP 请求以双换行分隔
      requests.forEach((request, index) => {
          if (request.startsWith('GET ') || request.startsWith('POST ')) {
              console.log(`Stream ${streamKey}, Request ${index + 1}:`);
              console.log(request.split('\r\n')[0]); // 打印请求行
          }
      });
  });
});

function cutBufferBeforeFirstZero(buffer) {
  for (let i = 0; i < buffer.length; i++) {
      if (buffer[i] === 0x00) {
          return buffer.slice(0, i);
      }
  }
  return buffer;
}
