{"_from": "jsrsasign", "_id": "jsrsasign@11.1.0", "_inBundle": false, "_integrity": "sha512-Ov74K9GihaK9/9WncTe1mPmvrO7Py665TUfUKvraXBpu+xcTWitrtuOwcjf4KMU9maPaYn0OuaWy0HOzy/GBXg==", "_location": "/jsrsasign", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "jsrsasign", "name": "jsrsasign", "escapedName": "jsrsasign", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/jsrsasign/-/jsrsasign-11.1.0.tgz", "_shasum": "195e788102731102fbf3e36b33fde28936f4bf57", "_spec": "jsrsasign", "_where": "D:\\project\\testP10cert", "author": {"name": "<PERSON><PERSON>"}, "badges": {"list": ["githubsponsors", "crypto"], "config": {"githubSponsorsUsername": "kjur", "cryptoURL": "https://github.com/kjur/jsrsasign#cryptocurrency"}}, "bugs": {"url": "https://github.com/kjur/jsrsasign/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "opensource free pure JavaScript cryptographic library supports RSA/RSAPSS/ECDSA/DSA signing/validation, ASN.1, PKCS#1/5/8 private/public key, X.509 certificate, CRL, OCSP, CMS SignedData, TimeStamp and CAdES and JSON Web Signature(JWS)/Token(JWT)/Key(JWK).", "devDependencies": {"mocha": "*"}, "funding": "https://github.com/kjur/jsrsasign#donations", "homepage": "http://kjur.github.io/jsrsasign/", "keywords": ["crypto", "cryptography", "Cipher", "RSA", "ECDSA", "DSA", "RSAPSS", "PKCS#1", "PKCS#5", "PKCS#8", "private key", "public key", "CSR", "PKCS#10", "hash function", "HMac", "ASN.1", "certificate", "X.509", "CRL", "OCSP", "RFC 3161", "Digital Timestamp", "Timestamp", "Time Stamp Token", "CMS", "Cryptgraphic Message Syntax", "PKCS#7", "Signature", "Digital Signature", "signing", "Message Digest", "JSON Web Token", "JWT", "JSON Web Signature", "JWS", "JSON Web Key", "JWK", "JOSE", "JWA"], "license": "MIT", "main": "lib/jsrsasign.js", "name": "jsrsasign", "repository": {"type": "git", "url": "git+https://github.com/kjur/jsrsasign.git"}, "scripts": {"test": "mocha"}, "version": "11.1.0"}