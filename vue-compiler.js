const fs = require('fs');
const path = require('path');
const vueCompiler = require('vue-template-compiler');

function generateUniqueId(componentName, uid) {
  return `${componentName}-${uid}`;
}

const modifyVueFile = (filePath) => {
  const content = fs.readFileSync(filePath, 'utf-8');
  const parsed = vueCompiler.parseComponent(content);
  const template = parsed.template ? parsed.template.content : '';
  if (!template) {
    console.log(`没有template的文件: ${filePath}`);
    return;
  }

  const modifiedTemplate = template.replace(/<\s*(el-input|el-button|el-select|el-radio-group|el-switch)([^>]*?)>/g, (match, componentName, attrs) => {
    // console.log(`Matched component: ${match}`);

    // 检查是否有id属性
    const idMatch = /id="([^"]+)"/.exec(attrs);
    let id = null;

    if (!idMatch) {
      const componentNameLower = componentName.toLowerCase();
      const uid = Math.random().toString(36).slice(2, 11); // 暂定随机uid
      id = generateUniqueId(componentNameLower, uid);
      // console.log(`Generated id: ${id}`);
      return `<${componentName} id="${id}"${attrs}>`;
    }
    return match;
  });
  // console.log('修改后 template content:\n', modifiedTemplate);

  // 替换template内容
  // 假设缩进使用两个空格
  const indentation = '    ';
  const indentedModifiedTemplate = modifiedTemplate.replace(/^/gm, indentation);
  const regex = /<template>([\s\S]*?)<\/template>/;
  const newContent = content.replace(regex, `<template>${indentedModifiedTemplate}</template>`);
  // console.log('写入后 to be written:\n', newContent);

  // 有变化，才写入文件
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf-8');
    console.log(`更新的文件: ${filePath}`);
  } else {
    console.log(`没有更新的文件: ${filePath}`);
  }
};

const modifyFilesInDirectory = (directoryPath) => {
  const files = fs.readdirSync(directoryPath);

  files.forEach((file) => {
    const filePath = path.join(directoryPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      modifyFilesInDirectory(filePath);
    } else if (file.endsWith('.vue')) {
      console.log(`比较文件: ${filePath}`);
      modifyVueFile(filePath);
    }
  });
};

const vueFilesDirectory = 'D:/project/sysmanage/sysvue/src/views';
modifyFilesInDirectory(vueFilesDirectory);
